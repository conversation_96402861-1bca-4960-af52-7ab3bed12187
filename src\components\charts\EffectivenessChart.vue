<script setup lang="ts">
import { computed } from 'vue'
import { VisXYContainer, VisAxis, VisLine } from '@unovis/vue'
import type { MatchResult } from '@/api/feathers-client'

interface Props {
  matchResults: MatchResult[]
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 300
})

// Chart data interface
interface ChartDataPoint {
  date: Date
  effectiveness: number
  points: number
  place: number | undefined
  matchName: string
  matchStartDate: string
}

// Transform match results into chart data
const chartData = computed(() => {
  // Filter out results without match data and create chart points
  const dataPoints: ChartDataPoint[] = props.matchResults
    .filter(result => result.match?.startDate) // Only include results with match startDate
    .map(result => ({
      date: new Date(result.match!.startDate!), // Use match.startDate
      effectiveness: (result.points / result.maxPoints) * 100,
      points: result.points,
      place: result.place,
      matchName: result.match!.name,
      matchStartDate: result.match!.startDate!
    }))
    .sort((a, b) => a.date.getTime() - b.date.getTime()) // Sort by match start date



  return dataPoints
})

// Chart configuration - use the data points directly
const x = (d: ChartDataPoint) => d.date
const y = (d: ChartDataPoint) => d.effectiveness

// Format functions
const xFormatter = (tick: number | Date) => {
  const date = tick instanceof Date ? tick : new Date(tick)
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  })
}

const yFormatter = (tick: number) => `${tick.toFixed(1)}%`
</script>

<template>
  <div class="w-full">
    <div v-if="chartData.length === 0" class="flex items-center justify-center h-64 text-muted-foreground">
      No match data available
    </div>
    <VisXYContainer
      v-else
      :data="chartData"
      :height="height"
      :margin="{ top: 20, right: 20, bottom: 60, left: 60 }"
    >
      <VisLine
        :x="x"
        :y="y"
        color="hsl(var(--primary))"
        :strokeWidth="2"
      />
      <VisAxis
        type="x"
        label="Date"
        :numTicks="Math.min(5, chartData.length)"
        :tickFormat="xFormatter"
        :gridLine="false"
      />
      <VisAxis
        type="y"
        label="Effectiveness (%)"
        :numTicks="5"
        :tickFormat="yFormatter"
      />
    </VisXYContainer>
  </div>
</template>

<style>
/* Customize chart colors to match the theme */
.vis-xy-container {
  --vis-primary-color: hsl(var(--primary));
  --vis-secondary-color: hsl(var(--secondary));
}
</style>
