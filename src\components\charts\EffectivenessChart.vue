<script setup lang="ts">
import { computed } from 'vue'
import { VisXYContainer, VisAxis, VisLine } from '@unovis/vue'
import type { MatchResult } from '@/api/feathers-client'

interface Props {
  matchResults: MatchResult[]
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 300
})

// Transform match results into chart data
const chartData = computed(() => {
  const data = props.matchResults
    .map((result, index) => ({
      date: new Date(result.resultDate),
      effectiveness: (result.points / result.maxPoints) * 100,
      match: result.match?.name || 'Unknown Match',
      originalIndex: index
    }))
    .sort((a, b) => a.date.getTime() - b.date.getTime())

  // Check if all dates are the same or very close (within 24 hours)
  const dateRange = data.length > 1 ?
    data[data.length - 1].date.getTime() - data[0].date.getTime() : 0
  const useDateAxis = dateRange > 24 * 60 * 60 * 1000 // More than 24 hours difference

  // If dates are too similar, use match sequence instead
  const finalData = data.map((item, index) => ({
    ...item,
    xValue: useDateAxis ? item.date : index + 1,
    displayValue: useDateAxis ? item.date.toLocaleDateString() : `Match ${index + 1}`
  }))



  return { data: finalData, useDateAxis }
})

// Chart configuration
const x = (d: any) => d.xValue
const y = (d: any) => d.effectiveness

// Format functions
const xFormatter = (tick: number | Date) => {
  const { useDateAxis } = chartData.value

  if (useDateAxis) {
    const date = tick instanceof Date ? tick : new Date(tick)
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  } else {
    const tickNumber = typeof tick === 'number' ? tick : 1
    return `Match ${Math.round(tickNumber)}`
  }
}

const yFormatter = (tick: number) => `${tick.toFixed(1)}%`
</script>

<template>
  <div class="w-full">
    <div v-if="chartData.data.length === 0" class="flex items-center justify-center h-64 text-muted-foreground">
      No match data available
    </div>
    <VisXYContainer
      v-else
      :data="chartData.data"
      :height="height"
      :margin="{ top: 20, right: 20, bottom: 60, left: 60 }"
    >
      <VisLine
        :x="x"
        :y="y"
        color="hsl(var(--primary))"
        :strokeWidth="2"
      />
      <VisAxis
        type="x"
        :label="chartData.useDateAxis ? 'Date' : 'Match Sequence'"
        :numTicks="Math.min(5, chartData.data.length)"
        :tickFormat="xFormatter"
        :gridLine="false"
      />
      <VisAxis
        type="y"
        label="Effectiveness (%)"
        :numTicks="5"
        :tickFormat="yFormatter"
      />
    </VisXYContainer>
  </div>
</template>

<style>
/* Customize chart colors to match the theme */
.vis-xy-container {
  --vis-primary-color: hsl(var(--primary));
  --vis-secondary-color: hsl(var(--secondary));
}
</style>
