<script setup lang="ts">
import { computed } from 'vue'
import { VisXYContainer, VisAxis, VisLine } from '@unovis/vue'
import type { MatchResult } from '@/api/feathers-client'

interface Props {
  matchResults: MatchResult[]
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 300
})

// Transform match results into chart data
const chartData = computed(() => {
  return props.matchResults
    .map(result => ({
      date: new Date(result.resultDate),
      effectiveness: (result.points / result.maxPoints) * 100,
      match: result.match?.name || 'Unknown Match'
    }))
    .sort((a, b) => a.date.getTime() - b.date.getTime())
})

// Chart configuration
const x = (d: any) => d.date
const y = (d: any) => d.effectiveness

// Format functions
const xFormatter = (tick: Date) => {
  return tick.toLocaleDateString('en-US', { 
    month: 'short', 
    year: '2-digit' 
  })
}

const yFormatter = (tick: number) => `${tick.toFixed(1)}%`
</script>

<template>
  <div class="w-full">
    <VisXYContainer 
      :data="chartData" 
      :height="height"
      :margin="{ top: 20, right: 20, bottom: 40, left: 60 }"
    >
      <VisLine 
        :x="x" 
        :y="y"
        color="hsl(var(--primary))"
        :curveType="'curveMonotoneX'"
      />
      <VisAxis 
        type="x" 
        label="Date"
        :numTicks="5"
        :tickFormat="xFormatter"
        :gridLine="false"
      />
      <VisAxis 
        type="y" 
        label="Effectiveness (%)"
        :numTicks="5"
        :tickFormat="yFormatter"
      />
    </VisXYContainer>
  </div>
</template>

<style>
/* Customize chart colors to match the theme */
.vis-xy-container {
  --vis-primary-color: hsl(var(--primary));
  --vis-secondary-color: hsl(var(--secondary));
}
</style>
