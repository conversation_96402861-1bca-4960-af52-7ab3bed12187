<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { useMatchesService } from '@/stores/matches'
import { Medal, Target, Users, Award, TrendingUp, MapPin, Trophy } from 'lucide-vue-next'
import { Card, CardContent } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Skeleton } from '@/components/ui/skeleton'
import type { MatchResult } from '@/api/feathers-client'

const { t } = useI18n()
const userStore = useUserStore()
const matchesService = useMatchesService()

// State
const isLoading = ref(false)
const error = ref<Error | null>(null)
const matchResults = ref<MatchResult[]>([])

// Computed statistics
const medalCounts = computed(() => {
  const gold = matchResults.value.filter(result => result.place === 1).length
  const silver = matchResults.value.filter(result => result.place === 2).length
  const bronze = matchResults.value.filter(result => result.place === 3).length
  return { gold, silver, bronze }
})

// Get medals in chronological order
const medalsChronological = computed(() => {
  const medals: Array<{ type: 'gold' | 'silver' | 'bronze', date: string }> = []

  matchResults.value
    .filter(result => result.place && result.place <= 3)
    .sort((a, b) => new Date(a.resultDate).getTime() - new Date(b.resultDate).getTime())
    .forEach(result => {
      if (result.place === 1) medals.push({ type: 'gold', date: result.resultDate })
      else if (result.place === 2) medals.push({ type: 'silver', date: result.resultDate })
      else if (result.place === 3) medals.push({ type: 'bronze', date: result.resultDate })
    })

  return medals
})

const totalMatches = computed(() => matchResults.value.length)

const averageEffectiveness = computed(() => {
  if (matchResults.value.length === 0) return 0
  const totalEffectiveness = matchResults.value.reduce((sum, result) => {
    return sum + (result.points / result.maxPoints) * 100
  }, 0)
  return Math.round((totalEffectiveness / matchResults.value.length) * 100) / 100
})

const maxEffectiveness = computed(() => {
  if (matchResults.value.length === 0) return 0
  const best = Math.max(...matchResults.value.map(result => (result.points / result.maxPoints) * 100))
  return Math.round(best * 100) / 100
})

const minEffectiveness = computed(() => {
  if (matchResults.value.length === 0) return 0
  const worst = Math.min(...matchResults.value.map(result => (result.points / result.maxPoints) * 100))
  return Math.round(worst * 100) / 100
})

const averageMedalPosition = computed(() => {
  if (matchResults.value.length === 0) return 0
  const totalPosition = matchResults.value.reduce((sum, result) => {
    return sum + (result.place || 0)
  }, 0)
  return Math.round((totalPosition / matchResults.value.length) * 100) / 100
})

const victories = computed(() => {
  return matchResults.value.filter(result => result.place === 1).length
})

const totalCompetitors = computed(() => {
  // This will be implemented later with proper data gathering
  return 12357
})

const maxCompetitors = computed(() => {
  // This will be implemented later with proper data gathering
  return 153
})

const minCompetitors = computed(() => {
  // This will be implemented later with proper data gathering
  return 50
})

const totalRivals = computed(() => {
  // This will be implemented later with proper data gathering
  return 35
})

const maxRivals = computed(() => {
  // This will be implemented later with proper data gathering
  return 153
})

const minRivals = computed(() => {
  // This will be implemented later with proper data gathering
  return 50
})

const averageAge = computed(() => {
  // This will be implemented later with proper data gathering
  return 35
})

const totalDistance = computed(() => {
  // This will be implemented later with proper data gathering
  return '12 635 km'
})

const maxDistance = computed(() => {
  // This will be implemented later with proper data gathering
  return '635 km'
})

// Fetch player results
async function fetchPlayerResults() {
  if (!userStore.activePlayer) {
    error.value = new Error('No active player selected')
    return
  }

  isLoading.value = true
  error.value = null

  try {
    const results = await matchesService.findMatchResults({
      query: {
        playerId: userStore.activePlayer.id,
        $populate: 'match',
        $limit: 1000,
        $sort: { resultDate: -1 }
      }
    })
    matchResults.value = results || []
  } catch (err) {
    console.error('Failed to fetch player results:', err)
    error.value = err instanceof Error ? err : new Error('Failed to fetch results')
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchPlayerResults()
})
</script>

<template>
  <div class="container mx-auto p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center gap-4 mb-8">
      <Avatar class="h-20 w-20">
        <AvatarImage v-if="userStore.activePlayer?.avatar" :src="userStore.activePlayer.avatar" />
        <AvatarFallback class="text-lg">
          {{ userStore.activePlayer?.firstname?.[0] }}{{ userStore.activePlayer?.lastname?.[0] }}
        </AvatarFallback>
      </Avatar>
      <div>
        <h1 class="text-3xl font-bold">
          {{ userStore.activePlayer?.firstname }} {{ userStore.activePlayer?.lastname }}
        </h1>
        <p class="text-muted-foreground">
          {{ t('statistics.playerStatistics') }}
        </p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card v-for="i in 3" :key="i">
          <CardHeader class="pb-2">
            <Skeleton class="h-4 w-20" />
          </CardHeader>
          <CardContent>
            <Skeleton class="h-8 w-16 mb-2" />
            <Skeleton class="h-3 w-24" />
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Error State -->
    <Card v-else-if="error" class="border-destructive">
      <CardContent class="pt-6">
        <p class="text-destructive">{{ error.message }}</p>
      </CardContent>
    </Card>

    <!-- Statistics Content -->
    <div v-else class="space-y-6">
      <!-- Medals Section -->
      <div class="mb-6">
        <h2 class="text-xl font-semibold mb-4">{{ t('statistics.medals') }}</h2>
        <div class="flex flex-wrap gap-1">
          <Medal
            v-for="(medal, index) in medalsChronological"
            :key="index"
            class="h-8 w-8"
            :class="{
              'text-yellow-500': medal.type === 'gold',
              'text-gray-400': medal.type === 'silver',
              'text-amber-600': medal.type === 'bronze'
            }"
            :title="`${medal.type} medal - ${new Date(medal.date).toLocaleDateString()}`"
          />
          <div v-if="medalsChronological.length === 0" class="text-muted-foreground text-sm">
            {{ t('statistics.noMedalsYet') }}
          </div>
        </div>
      </div>

      <!-- Statistics Grid -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Left Column -->
        <div class="space-y-4">
          <!-- Effectiveness -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Target class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.effectiveness') }}</div>
              <div class="text-lg font-semibold">{{ averageEffectiveness }}%</div>
            </div>
          </div>

          <!-- Max Effectiveness -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <TrendingUp class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.maxEffectiveness') }}</div>
              <div class="text-lg font-semibold">{{ maxEffectiveness }}%</div>
            </div>
          </div>

          <!-- Min Effectiveness -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <TrendingUp class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.minEffectiveness') }}</div>
              <div class="text-lg font-semibold">{{ minEffectiveness }}%</div>
            </div>
          </div>

          <!-- Average Medal Position -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Trophy class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.averageMedalPosition') }}</div>
              <div class="text-lg font-semibold">{{ averageMedalPosition }}</div>
            </div>
          </div>

          <!-- Victories -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Award class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.victories') }}</div>
              <div class="text-lg font-semibold">{{ victories }} - {{ totalMatches > 0 ? Math.round((victories / totalMatches) * 100) : 0 }}%</div>
            </div>
          </div>
        </div>

        <!-- Middle Column -->
        <div class="space-y-4">
          <!-- Total Matches -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Target class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.totalMatches') }}</div>
              <div class="text-lg font-semibold">{{ totalMatches }}</div>
            </div>
          </div>

          <!-- Total Competitors -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Users class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.totalCompetitors') }}</div>
              <div class="text-lg font-semibold">{{ totalCompetitors }}</div>
            </div>
          </div>

          <!-- Max Competitors -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Users class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.maxCompetitors') }}</div>
              <div class="text-lg font-semibold">{{ maxCompetitors }}</div>
            </div>
          </div>

          <!-- Min Competitors -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Users class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.minCompetitors') }}</div>
              <div class="text-lg font-semibold">{{ minCompetitors }}</div>
            </div>
          </div>

          <!-- Average Age -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Trophy class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.averageAge') }}</div>
              <div class="text-lg font-semibold">{{ averageAge }}</div>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-4">
          <!-- Total Rivals -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Target class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.totalRivals') }}</div>
              <div class="text-lg font-semibold">{{ totalRivals }}</div>
            </div>
          </div>

          <!-- Average Effectiveness (duplicate for layout) -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Target class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.averageEffectiveness') }}</div>
              <div class="text-lg font-semibold">{{ averageEffectiveness }}%</div>
            </div>
          </div>

          <!-- Max Rivals -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Users class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.maxRivals') }}</div>
              <div class="text-lg font-semibold">{{ maxRivals }}</div>
            </div>
          </div>

          <!-- Min Rivals -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Users class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.minRivals') }}</div>
              <div class="text-lg font-semibold">{{ minRivals }}</div>
            </div>
          </div>

          <!-- Average Age (duplicate for layout) -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <Trophy class="h-5 w-5 text-muted-foreground" />
            <div class="flex-1">
              <div class="text-sm text-muted-foreground">{{ t('statistics.averageAge') }}</div>
              <div class="text-lg font-semibold">{{ averageAge }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Distance Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
        <!-- Total Distance -->
        <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
          <MapPin class="h-5 w-5 text-muted-foreground" />
          <div class="flex-1">
            <div class="text-sm text-muted-foreground">{{ t('statistics.totalDistance') }}</div>
            <div class="text-lg font-semibold">{{ totalDistance }}</div>
          </div>
        </div>

        <!-- Max Distance -->
        <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
          <MapPin class="h-5 w-5 text-muted-foreground" />
          <div class="flex-1">
            <div class="text-sm text-muted-foreground">{{ t('statistics.maxDistance') }}</div>
            <div class="text-lg font-semibold">{{ maxDistance }}</div>
          </div>
        </div>
      </div>

      <!-- No Results Message -->
      <Card v-if="totalMatches === 0" class="text-center py-8">
        <CardContent>
          <Target class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 class="text-lg font-medium mb-2">{{ t('statistics.noResults') }}</h3>
          <p class="text-muted-foreground">{{ t('statistics.noResultsDescription') }}</p>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
