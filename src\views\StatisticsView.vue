<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { useMatchesService } from '@/stores/matches'
import { Trophy, Target, Users, Award, TrendingUp, Calendar, MapPin, Clock } from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import type { MatchResult } from '@/api/feathers-client'

const { t } = useI18n()
const userStore = useUserStore()
const matchesService = useMatchesService()

// State
const isLoading = ref(false)
const error = ref<Error | null>(null)
const matchResults = ref<MatchResult[]>([])

// Computed statistics
const medalCounts = computed(() => {
  const gold = matchResults.value.filter(result => result.place === 1).length
  const silver = matchResults.value.filter(result => result.place === 2).length
  const bronze = matchResults.value.filter(result => result.place === 3).length
  return { gold, silver, bronze }
})

const totalMatches = computed(() => matchResults.value.length)

const averageAccuracy = computed(() => {
  if (matchResults.value.length === 0) return 0
  const totalAccuracy = matchResults.value.reduce((sum, result) => {
    return sum + (result.points / result.maxPoints) * 100
  }, 0)
  return Math.round((totalAccuracy / matchResults.value.length) * 100) / 100
})

const bestAccuracy = computed(() => {
  if (matchResults.value.length === 0) return 0
  const best = Math.max(...matchResults.value.map(result => (result.points / result.maxPoints) * 100))
  return Math.round(best * 100) / 100
})

const worstAccuracy = computed(() => {
  if (matchResults.value.length === 0) return 0
  const worst = Math.min(...matchResults.value.map(result => (result.points / result.maxPoints) * 100))
  return Math.round(worst * 100) / 100
})

const totalPoints = computed(() => {
  return matchResults.value.reduce((sum, result) => sum + result.points, 0)
})

const averagePoints = computed(() => {
  if (matchResults.value.length === 0) return 0
  return Math.round((totalPoints.value / matchResults.value.length) * 100) / 100
})

const winRate = computed(() => {
  if (matchResults.value.length === 0) return 0
  const wins = matchResults.value.filter(result => result.place === 1).length
  return Math.round((wins / matchResults.value.length) * 100 * 100) / 100
})

const podiumRate = computed(() => {
  if (matchResults.value.length === 0) return 0
  const podiums = matchResults.value.filter(result => result.place && result.place <= 3).length
  return Math.round((podiums / matchResults.value.length) * 100 * 100) / 100
})

// Fetch player results
async function fetchPlayerResults() {
  if (!userStore.activePlayer) {
    error.value = new Error('No active player selected')
    return
  }

  isLoading.value = true
  error.value = null

  try {
    const results = await matchesService.findMatchResults({
      query: {
        playerId: userStore.activePlayer.id,
        $populate: 'match',
        $limit: 1000,
        $sort: { resultDate: -1 }
      }
    })
    matchResults.value = results || []
  } catch (err) {
    console.error('Failed to fetch player results:', err)
    error.value = err instanceof Error ? err : new Error('Failed to fetch results')
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchPlayerResults()
})
</script>

<template>
  <div class="container mx-auto p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center gap-4 mb-8">
      <Avatar class="h-20 w-20">
        <AvatarImage :src="userStore.activePlayer?.avatar" />
        <AvatarFallback class="text-lg">
          {{ userStore.activePlayer?.firstname?.[0] }}{{ userStore.activePlayer?.lastname?.[0] }}
        </AvatarFallback>
      </Avatar>
      <div>
        <h1 class="text-3xl font-bold">
          {{ userStore.activePlayer?.firstname }} {{ userStore.activePlayer?.lastname }}
        </h1>
        <p class="text-muted-foreground">
          {{ t('statistics.playerStatistics') }}
        </p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card v-for="i in 3" :key="i">
          <CardHeader class="pb-2">
            <Skeleton class="h-4 w-20" />
          </CardHeader>
          <CardContent>
            <Skeleton class="h-8 w-16 mb-2" />
            <Skeleton class="h-3 w-24" />
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Error State -->
    <Card v-else-if="error" class="border-destructive">
      <CardContent class="pt-6">
        <p class="text-destructive">{{ error.message }}</p>
      </CardContent>
    </Card>

    <!-- Statistics Content -->
    <div v-else class="space-y-6">
      <!-- Medals Section -->
      <div class="flex items-center gap-4 mb-4">
        <h2 class="text-xl font-semibold">{{ t('statistics.medals') }}</h2>
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-1">
            <Trophy class="h-5 w-5 text-yellow-500" />
            <span class="font-medium">{{ medalCounts.gold }}</span>
          </div>
          <div class="flex items-center gap-1">
            <Trophy class="h-5 w-5 text-gray-400" />
            <span class="font-medium">{{ medalCounts.silver }}</span>
          </div>
          <div class="flex items-center gap-1">
            <Trophy class="h-5 w-5 text-amber-600" />
            <span class="font-medium">{{ medalCounts.bronze }}</span>
          </div>
        </div>
      </div>

      <!-- Statistics Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Total Matches -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">{{ t('statistics.totalMatches') }}</CardTitle>
            <Target class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ totalMatches }}</div>
          </CardContent>
        </Card>

        <!-- Average Accuracy -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">{{ t('statistics.averageAccuracy') }}</CardTitle>
            <TrendingUp class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ averageAccuracy }}%</div>
          </CardContent>
        </Card>

        <!-- Best Accuracy -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">{{ t('statistics.bestAccuracy') }}</CardTitle>
            <Award class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ bestAccuracy }}%</div>
          </CardContent>
        </Card>

        <!-- Win Rate -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">{{ t('statistics.winRate') }}</CardTitle>
            <Trophy class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ winRate }}%</div>
          </CardContent>
        </Card>

        <!-- Podium Rate -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">{{ t('statistics.podiumRate') }}</CardTitle>
            <Award class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ podiumRate }}%</div>
          </CardContent>
        </Card>

        <!-- Average Points -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">{{ t('statistics.averagePoints') }}</CardTitle>
            <Target class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ averagePoints }}</div>
          </CardContent>
        </Card>
      </div>

      <!-- No Results Message -->
      <Card v-if="totalMatches === 0" class="text-center py-8">
        <CardContent>
          <Target class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 class="text-lg font-medium mb-2">{{ t('statistics.noResults') }}</h3>
          <p class="text-muted-foreground">{{ t('statistics.noResultsDescription') }}</p>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
